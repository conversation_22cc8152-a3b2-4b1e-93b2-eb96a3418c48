{"name": "dalelarroder.com", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "serve": "next start", "lint": "next lint --dir app", "prepare": "husky"}, "dependencies": {"@octokit/graphql": "^8.2.1", "@radix-ui/react-tooltip": "1.1.8", "@tailwindcss/postcss": "^4.0.15", "@types/node": "22.14.0", "@typescript-eslint/eslint-plugin": "8.29.1", "@typescript-eslint/parser": "8.29.1", "@vercel/analytics": "1.5.0", "@vercel/speed-insights": "^1.0.2", "classnames": "2.5.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "eslint": "9.23.0", "eslint-config-next": "15.2.4", "eslint-config-prettier": "10.1.1", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.5", "gsap": "3.12.7", "husky": "9.1.7", "lenis": "^1.2.3", "lint-staged": "15.5.0", "logrocket": "9.0.2", "motion": "12.9.2", "next": "15.2.4", "next-mdx-remote": "5.0.0", "next-themes": "0.4.6", "postcss": "8.5.3", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react": "^19.1.0", "react-dom": "19.1.0", "react-icons": "5.5.0", "rehype-pretty-code": "0.14.1", "server-only": "^0.0.1", "shiki": "3.2.1", "styled-components": "^6.1.19", "swr": "^2.3.3", "tailwindcss": "^4.0.15", "typescript": "5.8.2", "use-breakpoint": "4.0.6"}, "lint-staged": {"*.+(js|jsx|ts|tsx)": ["eslint --fix"], "*.+(js|jsx|ts|tsx|json|css|md|mdx)": ["prettier --write"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "devDependencies": {"@types/react": "^19.1.8", "@types/styled-components": "^5.1.34"}}