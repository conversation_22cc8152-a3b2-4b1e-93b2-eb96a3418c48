# dalelarroder.com ⚡️

- **Framework**: [Next.js](https://nextjs.org/)
- **Deployment**: [Vercel](https://vercel.com)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **Analytics**: [Logrocket](https://logrocket.com/)
- **Content**: [MDX](https://mdxjs.com/)

## Running Locally

### Installation

1. Clone this repo

```bash
<NAME_EMAIL>:dlarroder/dalelarroder.git
```

2. Change directory

```sh
cd dalelarroder
```

3. Install dependencies

```bash
bun install
```

1. Create a `.env.local` file following the `.env.example`

```bash
cp .env.example .env.local
```

5. Add your environment variables to `.env.local`

```txt
SPOTIFY_REFRESH_TOKEN=<Your env>
SPOTIFY_CLIENT_SECRET=<Your env>
SPOTIFY_CLIENT_ID=<Your env>
// ...
```

6. Run the development server

```bash
bun run dev
```

## Previous Version

This is the second version of my website.

Prevoius v1 version:

- https://v1.dalelarroder.com/

## Licence

[MIT](https://github.com/dlarroder/dalelarroder/blob/master/LICENSE) © [Akshat Majila](https://www.dalelarroder.com)
