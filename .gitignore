# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.env

# testing
/coverage

# next.js
/.next/
/out/
public/sitemap.xml
.vercel

# production
/build
*.xml
# rss feed
/public/feed.xml

# misc
.DS_Store

# debug
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local


# Contentlayer
.contentlayer
.env*.local
