@import 'tailwindcss';

@config '../tailwind.config.js';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@theme inline {
  --animate-first: moveVertical 30s ease infinite;
  --animate-second: moveInCircle 20s reverse infinite;
  --animate-third: moveInCircle 40s linear infinite;
  --animate-fourth: moveHorizontal 40s ease infinite;
  --animate-fifth: moveInCircle 20s ease infinite;

  @keyframes moveHorizontal {
    0% {
      transform: translateX(-50%) translateY(-10%);
    }
    50% {
      transform: translateX(50%) translateY(10%);
    }
    100% {
      transform: translateX(-50%) translateY(-10%);
    }
  }

  @keyframes moveInCircle {
    0% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes moveVertical {
    0% {
      transform: translateY(-50%);
    }
    50% {
      transform: translateY(50%);
    }
    100% {
      transform: translateY(-50%);
    }
  }
}

html::-webkit-scrollbar {
  display: none;
}

/* https://stackoverflow.com/questions/61083813/how-to-avoid-internal-autofill-selected-style-to-be-applied */
input:-webkit-autofill,
input:-webkit-autofill:focus {
  transition:
    background-color 600000s 0s,
    color 600000s 0s;
}

.underline-magical {
  @apply bg-linear-to-r from-primary-500 to-primary-500 bg-no-repeat 
  no-underline [background-position:0_100%]
  [background-size:100%_0.2em] focus:[background-size:100%_100%] hover:text-white
  hover:[background-size:100%_100%] motion-safe:transition-all
  motion-safe:duration-300 dark:from-primary-500 dark:to-primary-500;
}

.horizontal-underline {
  border-bottom-width: 0;
  background-image:
    linear-gradient(transparent, transparent), linear-gradient(#de1d8d, #de1d8d);
  background-size: 0 3px;
  background-position: 0 100%;
  background-repeat: no-repeat;
  transition: background-size 0.5s ease-in-out;
}

.horizontal-underline:hover {
  background-size: 100% 3px;
  background-position: 0 100%;
}

.horizontal-underline-active {
  background-size: 100% 3px;
  background-position: 0 100%;
}

#nprogress .bar {
  background: #de1d8d !important;
  height: 4px !important;
}

.intro-text {
  transition: opacity 0.4s;
}
