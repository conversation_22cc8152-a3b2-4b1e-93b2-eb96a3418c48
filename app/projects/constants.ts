import { Project } from './types';

export const projects: Project[] = [
  {
    title: 'Aphex Planner',
    src: 'planner-app.webp',
    color: '#dbeafe',
    url: 'https://app.aphex.co/',
    role: 'Fullstack Developer',
  },
  {
    title: 'Aphex Field',
    src: 'field-app.webp',
    color: '#ddd6fe',
    url: 'https://app.aphex.co/',
    role: 'Fullstack Developer',
  },
  {
    title: 'Aphex Publication',
    src: 'publication-app.webp',
    color: '#fae8ff',
    url: 'https://app.aphex.co/',
    role: 'Fullstack Developer',
  },
  {
    title: 'Spoken',
    src: 'spoken.webp',
    color: '#fee2e2',
    url: 'https://www.spoken.io/',
    role: 'Fullstack Developer',
  },
  {
    title: 'Topography Health',
    src: 'topo.webp',
    color: '#dbeafe',
    url: 'https://www.jointopo.com/',
    role: 'Backend Developer',
  },
  {
    title: 'SRI Big Data',
    src: 'bigdata.webp',
    color: '#ddd6fe',
    url: 'https://bigdata.sri.com.sg',
    role: 'Frontend Developer',
  },
  {
    title: 'Mathgame',
    src: 'mathgame.png',
    color: '#ffedd5',
    url: 'https://mathgame.dalelarroder.com/',
    role: 'Frontend Developer',
  },
  {
    title: 'Snakegame',
    src: 'snakegame.png',
    color: '#ecfccb',
    url: 'https://snakegame.dalelarroder.com/',
    role: 'Frontend Developer',
  },
];
