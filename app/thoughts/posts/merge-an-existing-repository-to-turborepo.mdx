---
title: 'How to merge an existing git repository into Turborepo'
publishedAt: '2024-11-26'
summary: 'A guide on how to merge any existing app or library into Turborepo.'
---

### Intro

Recently I have been challenged with the task of merging an existing repository into a [Turborepo](https://turbo.build/repo/docs) monorepo. This is a common task when migrating to a monorepo, and it can be a bit tricky if you are not familiar with the process. In this article, I will guide you through the steps to merge a git repository into Turborepo.

I won't be explaining what a monorepo is in this article, but if you are interested in learning more about it, I have made a similar article on [how to merge a git repository into Nx](https://www.dalelarroder.com/blog/how-to-merge-a-repository-into-nx).

### Step 1: Clone the source repository

The first step is to clone the repository you want to merge into the Turborepo monorepo. You can do this by running the following command:

```bash
git clone <repo-url> source-repo
```

Change the directory to the cloned repository:

```bash
cd source-repo
```

### Step 2: Filter the source repository into its own subdirectory

For this step we will use the [git filter-repo](https://github.com/newren/git-filter-repo) tool to filter the source repository into its own subdirectory, this will also filter the history of the repository.

```bash
git filter-repo --to-subdirectory-filter folder-name
```

Replace `folder-name` with the desired subdirectory for this repository in the monorepo.

### Step 3: Push the Filtered Repo to the Monorepo

Add the monorepo as a remote:

```bash
git remote add monorepo <monorepo-url>
```

Push the filtered history to a unique branch (e.g., old-repo-1):

```bash
git push monorepo HEAD:refs/heads/old-repo-1
```

<br />

# In the monorepo

Fetch and merge the source repository history into the monorepo:

```bash
git fetch origin old-repo-1
git merge --allow-unrelated-histories old-repo-1
```

This command will merge the source repository history into our Turborepo, now you just need to move your files to the correct [location](https://turbo.build/repo/docs/crafting-your-repository/structuring-a-repository#anatomy-of-a-workspace) in the monorepo and you are good to go.

I hope this article was helpful to you, if you have any questions or suggestions, feel free to reach out to me on [X](https://twitter.com/dalelarroder).
